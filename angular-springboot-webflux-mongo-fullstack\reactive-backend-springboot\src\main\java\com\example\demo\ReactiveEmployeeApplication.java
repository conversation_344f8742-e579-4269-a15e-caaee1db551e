package com.example.demo;

import com.mongodb.*;
import com.mongodb.reactivestreams.client.MongoClient;
import com.mongodb.reactivestreams.client.MongoClients;
import com.mongodb.reactivestreams.client.MongoDatabase;
import org.bson.Document;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@SpringBootApplication(
        exclude = {
                org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration.class,
                org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration.class
        }
)
public class ReactiveEmployeeApplication {

    public static void main(String[] args) {


//            String connectionString = "mongodb+srv://sudhakar1020y:<EMAIL>/?retryWrites=true&w=majority&appName=migration-cluster-dev";
//            ServerApi serverApi = ServerApi.builder()
//                    .version(ServerApiVersion.V1)
//                    .build();
//            MongoClientSettings settings = MongoClientSettings.builder()
//                    .applyConnectionString(new ConnectionString(connectionString))
//                    .serverApi(serverApi)
//                    .build();
//            // Create a new client and connect to the server
//            try (MongoClient mongoClient = MongoClients.create(settings)) {
//                try {
//                    // Send a ping to confirm a successful connection
//                    MongoDatabase database = mongoClient.getDatabase("device-catalog-db");
//                    database.runCommand(new Document("ping", 1));
//                    System.out.println("Pinged your deployment. You successfully connected to MongoDB!");
//                } catch (MongoException e) {
//                    e.printStackTrace();
//                }
//            }



        SpringApplication.run(ReactiveEmployeeApplication.class, args);
    }

/*
    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/api/**")
                        .allowedOrigins("http://localhost:3000", "http://localhost:8081")
                        .allowedMethods("GET", "POST", "PUT", "DELETE")
                        .allowCredentials(true);
            }
        };
    }
    */


    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**")  // Allow all endpoints
                        .allowedOrigins("http://localhost:3000")
                        .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                        .allowedHeaders("*")
                        .allowCredentials(true);
            }
        };
    }


}