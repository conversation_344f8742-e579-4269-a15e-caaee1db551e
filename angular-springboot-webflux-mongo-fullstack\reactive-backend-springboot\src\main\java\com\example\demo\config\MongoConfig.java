package com.example.demo.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.ServerApi;
import com.mongodb.ServerApiVersion;
import com.mongodb.reactivestreams.client.MongoClient;
import com.mongodb.reactivestreams.client.MongoClients;
import org.springframework.boot.autoconfigure.mongo.MongoConnectionDetails;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;

@Configuration
public class MongoConfig {

    private static final String CONNECTION_STRING =
            //"mongodb+srv://sudhakar:<EMAIL>/device-catalog-db?authSource=admin&retryWrites=true&w=majority&appName=migration-cluster-dev";
    "mongodb://localhost:27017/mydatabase";
    private static final String DATABASE = "device-catalog-db";

    /**
     * Required bean for Spring Boot 3.1+ reactive auto-configuration
     */
    @Bean
    public MongoConnectionDetails mongoConnectionDetails() {
        return () -> new ConnectionString(CONNECTION_STRING);
    }

    /**
     * Reactive MongoClient bean
     */
    @Bean
    public MongoClient reactiveMongoClient(MongoConnectionDetails details) {
        ServerApi serverApi = ServerApi.builder()
                .version(ServerApiVersion.V1)
                .build();

        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(details.getConnectionString())
                .serverApi(serverApi)
                .build();

        return MongoClients.create(settings);
    }

    /**
     * ReactiveMongoTemplate bean
     */
    @Bean
    public ReactiveMongoTemplate reactiveMongoTemplate(MongoClient mongoClient) {
        return new ReactiveMongoTemplate(mongoClient, DATABASE);
    }
}
