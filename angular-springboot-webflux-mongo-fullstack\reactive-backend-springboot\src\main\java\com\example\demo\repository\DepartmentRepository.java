package com.example.demo.repository;

import com.example.demo.model.Department;
import com.example.demo.model.Employee;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface DepartmentRepository extends ReactiveMongoRepository<Department, String> {
    Mono<Department> findFirstByCode(String code);

    // Or if you want all results
    Flux<Department> findByCode(String code);

    Flux<Department> findByEmployeeId(int employeeId);

    Mono<Department> findFirstByEmployeeId(int employeeId);


}
