package com.example.demo.repository;

import com.example.demo.model.Department;
import com.example.demo.model.DeviceCatalog;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface DeviceCatalogRepository extends ReactiveMongoRepository<DeviceCatalog, String> {
    Mono<DeviceCatalog> findFirstByAge(String code);

    // Or if you want all results
    Flux<DeviceCatalog> findByName(String name);




}
