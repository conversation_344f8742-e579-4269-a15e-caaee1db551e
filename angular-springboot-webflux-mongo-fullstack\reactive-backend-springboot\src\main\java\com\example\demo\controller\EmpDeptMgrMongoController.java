package com.example.demo.controller;

import com.example.demo.exception.ResourceNotFoundException;
import com.example.demo.model.Department;
import com.example.demo.model.Employee;
import com.example.demo.model.Manager;
import com.example.demo.repository.DepartmentRepository;
import com.example.demo.repository.EmployeeRepository;
import com.example.demo.repository.ManagerRepository;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/employee-department-manager")
public class EmpDeptMgrMongoController {




        private final EmployeeRepository employeeRepo;
        private final DepartmentRepository departmentRepo;
        private final ManagerRepository managerRepo;

        public EmpDeptMgrMongoController(EmployeeRepository employeeRepo, DepartmentRepository departmentRepo, ManagerRepository managerRepo) {
            this.employeeRepo = employeeRepo;
            this.departmentRepo = departmentRepo;
            this.managerRepo = managerRepo;
        }

        @GetMapping("/employee-data")
        public Mono<Map<String, Object>> getEmployeeData() {
            int employeeId = 101;
            String code = "ENG-01";
            int experience = 10;


            Mono<Employee> employeeMono = employeeRepo.findFirstByEmployeeId(employeeId)
                    .switchIfEmpty(Mono.error(new ResourceNotFoundException("Employee not found with id " + employeeId)));

            Mono<Department> departmentMono = departmentRepo.findFirstByCode(code)
                    .switchIfEmpty(Mono.error(new ResourceNotFoundException("Department not found with code " + code)));

            Mono<Manager> managerMono = managerRepo.findFirstByExperience(experience)
                    .switchIfEmpty(Mono.error(new ResourceNotFoundException("Manager not found with id " + experience)));

            return Mono.zip(employeeMono, departmentMono, managerMono)
                    .map(tuple -> {
                        Map<String, Object> result = new HashMap<>();
                        result.put("employee", tuple.getT1());
                        result.put("department", tuple.getT2());
                        result.put("manager", tuple.getT3());
                        return result;
                    });
        }

}
