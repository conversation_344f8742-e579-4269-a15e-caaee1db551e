package com.example.demo.controller;

import com.example.demo.service.SetupDataService;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

//@Component
public class DataInitializer {

    private final SetupDataService setupDataService;

    public DataInitializer(SetupDataService setupDataService) {
        this.setupDataService = setupDataService;
    }

    @PostConstruct
    public void init() {
        setupDataService.insertSampleData()
                .subscribe(
                        unused -> {},
                        error -> System.err.println("Error inserting data: " + error),
                        () -> System.out.println("Sample data inserted successfully.")
                );
    }
}
