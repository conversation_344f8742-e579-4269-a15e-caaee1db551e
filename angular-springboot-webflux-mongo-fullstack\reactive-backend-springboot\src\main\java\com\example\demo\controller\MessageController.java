package com.example.demo.controller;

import com.example.demo.model.Message;
import com.example.demo.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/messages")
public class MessageController {
    private final MessageService messageService;

    @Autowired
    public MessageController(MessageService messageService) {
        this.messageService = messageService;
    }

    @GetMapping
    public Flux<Message> getMessages() {
        return messageService.getAllMessages();
    }

    @PostMapping
    public Mono<Message> createMessage(@RequestBody Message message) {
        if (message == null) {
            System.out.println("Message object is null!");
        } else {
            System.out.println("Message content: " + message.getContent());
        }
        return messageService.saveMessage(message);
    }
}
