/*
package com.example.demo.controller;


import com.example.demo.exception.ResourceNotFoundException;
import com.example.demo.model.Department;
import com.example.demo.model.Employee;
import com.example.demo.model.Manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import reactor.core.publisher.Mono;

import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.HashMap;
import java.util.Map;


public class EmpDeptManagerDynamoDbController {
}



@RestController
@RequestMapping("/api")
public class EmployeeController {

    @Autowired
    private DynamoDbAsyncTable<Employee> employeeTable;

    @Autowired
    private DynamoDbAsyncTable<Department> departmentTable;

    @Autowired
    private DynamoDbAsyncTable<Manager> managerTable;

    @GetMapping("/employee-data")
    public Mono<Map<String, Object>> getEmployeeData() {
        Mono<Employee> employeeMono = getEmployee(101);
        Mono<Department> departmentMono = getDepartment("ENG-01");
        Mono<Manager> managerMono = getManager(8);

        return Mono.zip(
                employeeMono.onErrorResume(ex -> Mono.just(new Employee("Error", -1))),
                departmentMono.onErrorResume(ex -> Mono.just(new Department("Error", "ERR"))),
                managerMono.onErrorResume(ex -> Mono.just(new Manager("Error", -1)))
        ).map(tuple -> {
            Map<String, Object> results = new HashMap<>();
            results.put("employee", tuple.getT1());
            results.put("department", tuple.getT2());
            results.put("manager", tuple.getT3());
            return results;
        });
    }

    private Mono<Employee> getEmployee(int id) {
        return Mono.fromFuture(() -> employeeTable.getItem(getKey(id)))
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Employee not found")));
    }

    private Mono<Department> getDepartment(String code) {
        return Mono.fromFuture(() -> departmentTable.getItem(getKey(code)))
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Department not found")));
    }

    private Mono<Manager> getManager(int id) {
        return Mono.fromFuture(() -> managerTable.getItem(getKey(id)))
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Manager not found")));
    }

    private Key getKey(int id) {
        return Key.builder().partitionValue(id).build();
    }

    private Key getKey(String code) {
        return Key.builder().partitionValue(code).build();
    }
}


 */