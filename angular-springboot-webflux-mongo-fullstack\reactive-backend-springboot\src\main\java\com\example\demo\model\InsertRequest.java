package com.example.demo.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;

public class InsertRequest {
    @NotBlank(message = "Department name is required")
    private String deptName;

    @NotBlank(message = "Department code is required")
    private String deptCode;

    @NotBlank(message = "Employee name is required")
    private String empName;

    @NotNull(message = "Employee ID is required")
    @Min(value = 1, message = "Employee ID must be positive")
    private int employeeId;

    @NotBlank(message = "Manager name is required")
    private String managerName;

    @NotNull(message = "Experience is required")
    @Min(value = 0, message = "Experience must be non-negative")
    private int experience;

    // Getters and setters


    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public int getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(int employeeId) {
        this.employeeId = employeeId;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public int getExperience() {
        return experience;
    }

    public void setExperience(int experience) {
        this.experience = experience;
    }
}