package com.example.demo.repository;

import com.example.demo.model.Employee;
import com.example.demo.model.Manager;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface EmployeeRepository extends ReactiveMongoRepository<Employee, String> {
    Mono<Employee> findFirstByEmployeeId(int employeeId);

    @Query("{ 'employeeId': ?0, 'departmentCode': ?1 }")
    Mono<Employee> findByEmpIdAndDept(int employeeId, String departmentCode);

    // Or if you want all results
    Flux<Employee> findByEmployeeId(int employeeId);

}