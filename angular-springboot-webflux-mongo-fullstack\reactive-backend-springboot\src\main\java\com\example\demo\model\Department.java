package com.example.demo.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "departments")
public class Department {
    @Id
    private String id;
    private String name;
    private String code;
    private int employeeId;
    public Department() {

    }

    public Department(String id, String name, String code,  int employeeId) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.employeeId = employeeId;
    }

    public Department(String id, String name, String code) {
        this.id = id;
        this.name = name;
        this.code = code;
    }


    public Department(String error, String err) {
        this.name = error;
        this.code = err;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(int employeeId) {
        this.employeeId = employeeId;
    }
}