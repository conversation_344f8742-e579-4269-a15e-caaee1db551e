package com.example.demo.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "employees")
public class Employee {
    @Id
    private String id;
    private String name;
    private int employeeId;
    private String departmentCode;

    public Employee(String id, String name, int employeeId, String departmentCode) {
        this.id = id;
        this.name = name;
        this.employeeId = employeeId;
        this.departmentCode = departmentCode;
    }

    public Employee() {
    }

    public Employee(String error, int i) {
        this.name = error;
        this.employeeId = i;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(int employeeId) {
        this.employeeId = employeeId;
    }
}