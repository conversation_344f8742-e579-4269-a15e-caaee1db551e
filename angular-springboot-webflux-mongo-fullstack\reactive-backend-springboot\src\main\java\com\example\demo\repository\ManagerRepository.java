package com.example.demo.repository;

import com.example.demo.model.Department;
import com.example.demo.model.Manager;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface ManagerRepository extends ReactiveMongoRepository<Manager, String> {
    //Mono<Manager> findByExperience(int experience);
    Mono<Manager> findFirstByExperience(int experience);

    // Or if you want all results
    Flux<Manager> findByExperience(int experience);

    Flux<Manager> findByEmployeeId(int employeeId);

    Mono<Manager> findFirstByEmployeeId(int employeeId);
}
