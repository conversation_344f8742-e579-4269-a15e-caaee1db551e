package com.example.demo.controller;

import com.example.demo.model.InsertRequest;
import com.example.demo.repository.DepartmentRepository;
import com.example.demo.repository.EmployeeRepository;
import com.example.demo.repository.ManagerRepository;
import org.springframework.web.bind.annotation.*;

import com.example.demo.model.Employee;
import com.example.demo.model.Department;
import com.example.demo.model.Manager;
import com.example.demo.exception.ResourceNotFoundException;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;



@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "http://localhost:3000")
public class EmployeeController {

    private final EmployeeRepository employeeRepo;
    private final DepartmentRepository departmentRepo;
    private final ManagerRepository managerRepo;

    public EmployeeController(EmployeeRepository employeeRepo, DepartmentRepository departmentRepo, ManagerRepository managerRepo) {
        this.employeeRepo = employeeRepo;
        this.departmentRepo = departmentRepo;
        this.managerRepo = managerRepo;
    }

    // Helper method for sleeping
    private static void sleep(int seconds) {
        try {
            TimeUnit.SECONDS.sleep(seconds);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
    
    private Mono<Employee> getEmployee() {
        return Mono.fromSupplier(() -> {
            System.out.println("getEmployee::" + Thread.currentThread().getName());
            sleep(2);
            // Uncomment to test exception
            // throw new RuntimeException("Error in getEmployee");
            return new Employee(null, "John Doe", 101, "ENG-01");
        })
        .subscribeOn(Schedulers.boundedElastic())
        .onErrorResume(ex -> {
            System.err.println("Exception in getEmployee: " + ex.getMessage());
            return Mono.error(new ResourceNotFoundException("Employee data not found", ex));
        });
    }

    private Mono<Department> getDepartment() {
        return Mono.fromSupplier(() -> {
            System.out.println("getDepartment::" + Thread.currentThread().getName());
            sleep(1);
            // Uncomment to test exception
            // throw new RuntimeException("Error in getDepartment");
            return new Department(null, "Engineering", "ENG-01");
        })
        .subscribeOn(Schedulers.boundedElastic())
        .onErrorResume(ex -> {
            System.err.println("Exception in getDepartment: " + ex.getMessage());
            return Mono.error(new ResourceNotFoundException("Department data not found", ex));
        });
    }

    private Mono<Manager> getManager() {
        return Mono.fromSupplier(() -> {
            System.out.println("getManager::" + Thread.currentThread().getName());
            sleep(1);
            return new Manager(null, "Jane Smith", 10, 101);
        })
        .subscribeOn(Schedulers.boundedElastic());
    }

    @GetMapping("/employee-data2")
    public Mono<Map<String, Object>> getEmployeeData2() {
        // Create individual monos
        Mono<Employee> employeeMono = getEmployee();
        Mono<Department> departmentMono = getDepartment();
        Mono<Manager> managerMono = getManager();
        
        // Combine all monos using zip
        return Mono.zip(
            employeeMono.onErrorResume(ex -> {
                if (ex instanceof ResourceNotFoundException) {
                    return Mono.error(ex);
                }
                return Mono.just(new Employee("Error", -1));
            }),
            departmentMono.onErrorResume(ex -> {
                if (ex instanceof ResourceNotFoundException) {
                    return Mono.error(ex);
                }
                return Mono.just(new Department("Error", "ERR"));
            }),
            managerMono.onErrorResume(ex -> 
                Mono.just(new Manager("Error", -1))
            )
        ).map(tuple -> {
            Map<String, Object> results = new HashMap<>();
            results.put("employee", tuple.getT1());
            results.put("department", tuple.getT2());
            results.put("manager", tuple.getT3());
            return results;
        });
    }

    @GetMapping("/employee-data")
    public Flux<Map<String, Object>> getEmployeeData() {
        int employeeId = 101;
        String code = "ENG-01";
        int experience = 10;


        Flux<Employee> employeeMono = employeeRepo.findByEmployeeId(employeeId)
                .doOnNext(emp -> System.out.println("Repo thread: " + Thread.currentThread().getName()))
                .subscribeOn(Schedulers.boundedElastic());



        Flux<Employee> employeeMonoSample = employeeRepo.findByEmployeeId(employeeId)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Employee not found with id " + employeeId)));

        Flux<Department> departmentMono = departmentRepo.findByCode(code)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Department not found with code " + code)));

        Flux<Manager> managerMono = managerRepo.findByExperience(experience)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Manager not found with id " + experience)));

        return Flux.zip(employeeMono, departmentMono, managerMono)
                .map(tuple -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("employee", tuple.getT1());
                    result.put("department", tuple.getT2());
                    result.put("manager", tuple.getT3());
                    return result;
                });
    }

    @PostMapping("/insert")
    public Mono<String> insertData(@RequestBody InsertRequest request) {
        Department department = new Department(null, request.getDeptName(), request.getDeptCode());
        Employee employee = new Employee(null, request.getEmpName(), request.getEmployeeId(), request.getDeptCode());
        Manager manager = new Manager(null, request.getManagerName(), request.getExperience(), request.getEmployeeId());

        return departmentRepo.findFirstByCode(request.getDeptCode())
                .flatMap(dep -> Mono.error(new RuntimeException("Duplicate department code")))
                .switchIfEmpty(departmentRepo.save(department))
                .then(
                        employeeRepo.findFirstByEmployeeId(request.getEmployeeId())
                                .flatMap(emp -> Mono.error(new RuntimeException("Duplicate employeeId")))
                                .switchIfEmpty(employeeRepo.save(employee))
                )
                .then(
                        managerRepo.findFirstByExperience(request.getExperience())
                                .flatMap(mgr -> Mono.error(new RuntimeException("Duplicate manager for employee with same experience")))
                                .switchIfEmpty(managerRepo.save(manager))
                )
                .thenReturn("Inserted successfully");
    }



}


/*
what  is the diff between below methods and why the  second approach not calling the subscribeOn  method

private Mono<Employee> getEmployee() {
        return Mono.fromSupplier(() -> {
            System.out.println("getEmployee::" + Thread.currentThread().getName());
            sleep(2);
            // Uncomment to test exception
            // throw new RuntimeException("Error in getEmployee");
            return new Employee(null, "John Doe", 101, "ENG-01");
        })
        .subscribeOn(Schedulers.boundedElastic())
        .onErrorResume(ex -> {
            System.err.println("Exception in getEmployee: " + ex.getMessage());
            return Mono.error(new ResourceNotFoundException("Employee data not found", ex));
        });
    }


Flux<Employee> employeeMono = employeeRepo.findByEmployeeId(employeeId)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Employee not found with id " + employeeId)));



 */