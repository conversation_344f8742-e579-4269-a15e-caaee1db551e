package com.example.demo.service;

import com.example.demo.model.Department;
import com.example.demo.model.Employee;
import com.example.demo.model.Manager;
import com.example.demo.repository.DepartmentRepository;
import com.example.demo.repository.EmployeeRepository;
import com.example.demo.repository.ManagerRepository;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

//@Service
public class SetupDataService {

    private final EmployeeRepository employeeRepo;
    private final DepartmentRepository departmentRepo;
    private final ManagerRepository managerRepo;

    public SetupDataService(EmployeeRepository employeeRepo, DepartmentRepository departmentRepo, ManagerRepository managerRepo) {
        this.employeeRepo = employeeRepo;
        this.departmentRepo = departmentRepo;
        this.managerRepo = managerRepo;
    }

    public Mono<Void> insertSampleData() {
        Department department = new Department(null, "Engineering", "ENG-01", 101);
        Employee employee = new Employee(null, "<PERSON>", 101, "ENG-01");
        Manager manager = new Manager(null, "<PERSON>", 10, 101);

        /*
        Mono<Employee> existing = employeeRepo.findFirstByEmployeeId(101);
        existing.flatMap(emp -> {
            // employee already exists
            return Mono.error(new RuntimeException("Duplicate employeeId"));
        }).switchIfEmpty(
                // insert only if not found
                employeeRepo.save(employee)
        );

        Mono<Department> existingDept = departmentRepo.findFirstByCode("ENG-01");
        existingDept.flatMap(dep -> {
            // department already exists
            return Mono.error(new RuntimeException("Duplicate department code"));
        }).switchIfEmpty(
                // insert only if not found
                departmentRepo.save(department)
        );

        Mono<Manager> existingManager = managerRepo.findFirstByExperience(10);
        existingManager.flatMap(mgr -> {
            // manager already exists
            return Mono.error(new RuntimeException("Duplicate manager experience"));
        }).switchIfEmpty(
                // insert only if not found
                managerRepo.save(manager)
        );

        */


        /*
        return departmentRepo.save(department)
                .then(employeeRepo.save(employee))
                .then(managerRepo.save(manager))
                .then(); // return Mono<Void>
        */

        return departmentRepo.findFirstByCode("ENG-01")
                .flatMap(dep -> Mono.error(new RuntimeException("Duplicate department code")))
                .switchIfEmpty(departmentRepo.save(department))
                .then(
                        employeeRepo.findFirstByEmployeeId(101)
                                .flatMap(emp -> Mono.error(new RuntimeException("Duplicate employeeId")))
                                .switchIfEmpty(employeeRepo.save(employee))
                ).then(
                        managerRepo.findFirstByExperience(10)
                                .flatMap(mgr -> Mono.error(new RuntimeException("Duplicate manager experience")))
                                .switchIfEmpty(managerRepo.save(manager))
                ).then();
    }
}