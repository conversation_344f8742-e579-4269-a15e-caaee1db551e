# Maven build
target/
!.mvn/wrapper/maven-wrapper.jar

# Maven specific
.mvn/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws
out/

# Eclipse
.project
.classpath
.settings/
bin/

# VS Code
.vscode/

# OS generated
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Environment files
.env
*.env

# Spring Boot
*.jar
*.war
*.ear
spring.log

# Test reports
test-output/
surefire-reports/
jacoco.exec

# Coverage
coverage/
*.exec
*.ec

# Local config
application-local.yml
application-local.properties

# Node (if using frontend in same repo)
node_modules/
dist/

# Docker
*.pid
*.tar
docker-compose.override.yml

# Optional: IDE-specific logs
hs_err_pid*.log
