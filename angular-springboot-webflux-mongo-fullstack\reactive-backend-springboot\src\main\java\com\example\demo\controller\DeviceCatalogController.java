package com.example.demo.controller;

import com.example.demo.exception.ResourceNotFoundException;
import com.example.demo.model.*;
import com.example.demo.repository.DepartmentRepository;
import com.example.demo.repository.DeviceCatalogRepository;
import com.example.demo.repository.EmployeeRepository;
import com.example.demo.repository.ManagerRepository;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


//@RestController
//@RequestMapping("/devices")
//@CrossOrigin(origins = "http://localhost:3000")
public class DeviceCatalogController {

    private DeviceCatalogRepository deviceCatalogRepository;


    public DeviceCatalogController(DeviceCatalogRepository deviceCatalogRepository) {
        this.deviceCatalogRepository = deviceCatalogRepository;

    }

    // Helper method for sleeping
    private static void sleep(int seconds) {
        try {
            TimeUnit.SECONDS.sleep(seconds);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }



    @GetMapping(value="/all")
    public Flux<DeviceCatalog> getAllDevices() {
        return deviceCatalogRepository.findAll();
    }



    @GetMapping("/device-catalog-data")
    public Flux<Map<String, Object>> getEmployeeData() {

        String name = "Alice";


        Flux<DeviceCatalog> deviceCatalogMono = deviceCatalogRepository.findByName(name)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Manager not found with id " + name)));

        return deviceCatalogMono.map(deviceCatalog -> {
            Map<String, Object> result = new HashMap<>();
            result.put("deviceCatalog", deviceCatalog);
            return result;
        });
    }





}


