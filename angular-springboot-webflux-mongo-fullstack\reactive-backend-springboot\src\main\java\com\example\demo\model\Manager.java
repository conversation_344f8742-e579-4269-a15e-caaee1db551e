package com.example.demo.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "managers")
public class Manager {
    @Id
    private String id;
    private String name;
    private int experience;
    private int employeeId;
    
    public Manager() {}

    public Manager(String id, String name, int experience, int employeeId) {
        this.id = id;
        this.name = name;
        this.experience = experience;
        this.employeeId = employeeId;
    }

    public Manager(String error, int i) {
        this.name = error;
        this.experience = i;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getExperience() {
        return experience;
    }

    public void setExperience(int experience) {
        this.experience = experience;
    }

    public int getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(int employeeId) {
        this.employeeId = employeeId;
    }

}