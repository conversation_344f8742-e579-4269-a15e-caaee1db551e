package com.example.demo.controller;

import com.example.demo.exception.ResourceNotFoundException;
import com.example.demo.model.Department;
import com.example.demo.model.Employee;
import com.example.demo.model.InsertRequest;
import com.example.demo.model.Manager;
import com.example.demo.repository.DepartmentRepository;
import com.example.demo.repository.EmployeeRepository;
import com.example.demo.repository.ManagerRepository;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/employee-department-manager")
@CrossOrigin(origins = "http://localhost:3000")
@Validated
public class EmpDeptMgrMongoFluxController {




        private final EmployeeRepository employeeRepo;
        private final DepartmentRepository departmentRepo;
        private final ManagerRepository managerRepo;

        public EmpDeptMgrMongoFluxController(EmployeeRepository employeeRepo, DepartmentRepository departmentRepo, ManagerRepository managerRepo) {
            this.employeeRepo = employeeRepo;
            this.departmentRepo = departmentRepo;
            this.managerRepo = managerRepo;
        }

        @GetMapping("/employee-data-list")
        public Flux<Map<String, Object>> getEmployeeData() {
            // Get all employees
            Flux<Employee> employeeFlux = employeeRepo.findAll();
            
            // For each employee, get their department and manager
            return employeeFlux.flatMap(employee -> {
                int employeeId = employee.getEmployeeId();


                Flux<Department> departmentMono = departmentRepo.findByEmployeeId(employeeId)
                        .switchIfEmpty(Mono.error(new ResourceNotFoundException("Department not found with code " + employeeId)));

                Flux<Manager> managerMono = managerRepo.findByEmployeeId(employeeId)
                        .switchIfEmpty(Mono.error(new ResourceNotFoundException("Manager not found for employee " + employeeId)));
                
                return Flux.zip(Flux.just(employee), departmentMono, managerMono)
                        .map(tuple -> {
                            Map<String, Object> result = new HashMap<>();
                            result.put("employee", tuple.getT1());
                            result.put("department", tuple.getT2());
                            result.put("manager", tuple.getT3());
                            return result;
                        });
            });
        }

    @GetMapping("/employee-data/{employeeId}")
    public Flux<Map<String, Object>> getEmployeeDataById(@PathVariable int employeeId) {

        // You can fetch other fields dynamically or use hardcoded values for now
        //String code = "ENG-01";
        //int experience = 10;

        Flux<Employee> employeeFlux = employeeRepo.findByEmployeeId(employeeId)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Employee not found with id " + employeeId)));

        Flux<Department> departmentFlux = departmentRepo.findByEmployeeId(employeeId)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Department not found with code " + employeeId)));

        Flux<Manager> managerFlux = managerRepo.findByEmployeeId(employeeId)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Manager not found with experience " + employeeId)));

        return Flux.zip(employeeFlux, departmentFlux, managerFlux)
                .map(tuple -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("employee", tuple.getT1());
                    result.put("department", tuple.getT2());
                    result.put("manager", tuple.getT3());
                    return result;
                });
    }

    @PostMapping("/insert-data")
    public Mono<String> insertData(
            @RequestParam String deptName,
            @RequestParam String deptCode,
            @RequestParam String empName,
            @RequestParam int employeeId,
            @RequestParam String managerName,
            @RequestParam int experience
    ) {
        Department department = new Department(null, deptName, deptCode);
        Employee employee = new Employee(null, empName, employeeId, deptCode);
        Manager manager = new Manager(null, managerName, experience, employeeId);

        return departmentRepo.findFirstByCode(deptCode)
                .flatMap(dep -> Mono.error(new RuntimeException("Duplicate department code")))
                .switchIfEmpty(departmentRepo.save(department))
                .then(
                        employeeRepo.findFirstByEmployeeId(employeeId)
                                .flatMap(emp -> Mono.error(new RuntimeException("Duplicate employeeId")))
                                .switchIfEmpty(employeeRepo.save(employee))
                )
                .then(
                        managerRepo.findFirstByExperience(experience)
                                .flatMap(mgr -> Mono.error(new RuntimeException("Duplicate manager for employee with same experience")))
                                .switchIfEmpty(managerRepo.save(manager))
                )
                .thenReturn("Inserted successfully");
    }

    /*
    @PostMapping("/insert")
    public Mono<String> insertData(@RequestBody InsertRequest request) {
        Department department = new Department(null, request.getDeptName(), request.getDeptCode());
        Employee employee = new Employee(null, request.getEmpName(), request.getEmployeeId(), request.getDeptCode());
        Manager manager = new Manager(null, request.getManagerName(), request.getExperience(), request.getEmployeeId());

        return departmentRepo.findFirstByCode(request.getDeptCode())
                .flatMap(dep -> Mono.error(new RuntimeException("Duplicate department code")))
                .switchIfEmpty(departmentRepo.save(department))
                .then(
                        employeeRepo.findFirstByEmployeeId(request.getEmployeeId())
                                .flatMap(emp -> Mono.error(new RuntimeException("Duplicate employeeId")))
                                .switchIfEmpty(employeeRepo.save(employee))
                )
                .then(
                        managerRepo.findFirstByExperience(request.getExperience())
                                .flatMap(mgr -> Mono.error(new RuntimeException("Duplicate manager for employee with same experience")))
                                .switchIfEmpty(managerRepo.save(manager))
                )
                .thenReturn("Inserted successfully");
    }
    */

    @PostMapping("/insert")
    public Mono<String> insertData( @RequestBody @Valid InsertRequest request) {
        Department department = new Department(null, request.getDeptName(), request.getDeptCode(), request.getEmployeeId());
        Employee employee = new Employee(null, request.getEmpName(), request.getEmployeeId(), request.getDeptCode());
        Manager manager = new Manager(null, request.getManagerName(), request.getExperience(), request.getEmployeeId());

        return departmentRepo.findFirstByEmployeeId(request.getEmployeeId())
               // .flatMap(dep -> Mono.error(new RuntimeException()))
                .flatMap(dep -> {
                    if(dep.getEmployeeId() == request.getEmployeeId()) {
                        return Mono.error(new RuntimeException("Duplicate department code"));
                    } else {
                        return Mono.just(dep);
                    }
                })
                .switchIfEmpty(departmentRepo.save(department))
                .then(
                        employeeRepo.findFirstByEmployeeId(request.getEmployeeId())
                                .flatMap(emp -> Mono.error(new RuntimeException("Duplicate employeeId")))
                                .switchIfEmpty(employeeRepo.save(employee))
                )
                .then(
                        managerRepo.findFirstByEmployeeId(request.getEmployeeId())
                                .flatMap(mgr -> Mono.error(new RuntimeException("Duplicate manager for employee with same experience")))
                                .switchIfEmpty(managerRepo.save(manager))
                )
                .thenReturn("Inserted successfully");
    }

     @DeleteMapping("/delete/{employeeId}")
    public Mono<Map<String, String>> deleteEmployeeData(@PathVariable int employeeId) {
        // Find all related records first to confirm they exist
        Flux<Employee> employeeFlux = employeeRepo.findByEmployeeId(employeeId)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Employee not found with id " + employeeId)));

        Flux<Department> departmentFlux = departmentRepo.findByEmployeeId(employeeId)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Department not found for employee " + employeeId)));

        Flux<Manager> managerFlux = managerRepo.findByEmployeeId(employeeId)
                .switchIfEmpty(Mono.error(new ResourceNotFoundException("Manager not found for employee " + employeeId)));
    
        // Delete all related records - take the first of each flux for deletion
        return Flux.zip(employeeFlux, departmentFlux, managerFlux)
                .next() // Take the first tuple of results
                .flatMap(tuple -> {
                    Employee employee = tuple.getT1();
                    Department department = tuple.getT2();
                    Manager manager = tuple.getT3();
                    
                    return employeeRepo.delete(employee)
                            .then(departmentRepo.delete(department))
                            .then(managerRepo.delete(manager))
                            .thenReturn(Map.of(
                                "status", "success", 
                                "message", "Employee with ID " + employeeId + " and related records deleted successfully"
                            ));
                });
    }

}
